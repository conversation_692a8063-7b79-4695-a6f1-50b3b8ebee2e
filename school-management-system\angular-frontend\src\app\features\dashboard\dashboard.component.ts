import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { LayoutComponent } from '../../shared/components/layout/layout.component';
import { AuthService } from '../../core/services/auth.service';
import { environment } from '../../../environments/environment';

interface DashboardStats {
  total_students: number;
  total_teachers: number;
  total_classes: number;
  pending_fees: number;
  upcoming_events: number;
  upcoming_exams: number;
}

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, LayoutComponent, RouterLink],
  template: `
    <app-layout>
      <div class="dashboard-container fade-in">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h1 class="h3 mb-0">Dashboard</h1>
          <div class="text-muted">
            Welcome back, {{ currentUser?.first_name }}!
          </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
          <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card border-left-primary">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                      Total Students
                    </div>
                    <div class="h4 mb-0 font-weight-bold text-gray-800">
                      {{ stats?.total_students || 0 }}
                    </div>
                  </div>
                  <div class="col-auto">
                    <i class="fas fa-user-graduate fa-2x text-gray-300"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card border-left-success">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                      Total Teachers
                    </div>
                    <div class="h4 mb-0 font-weight-bold text-gray-800">
                      {{ stats?.total_teachers || 0 }}
                    </div>
                  </div>
                  <div class="col-auto">
                    <i class="fas fa-chalkboard-teacher fa-2x text-gray-300"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card border-left-info">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                      Total Classes
                    </div>
                    <div class="h4 mb-0 font-weight-bold text-gray-800">
                      {{ stats?.total_classes || 0 }}
                    </div>
                  </div>
                  <div class="col-auto">
                    <i class="fas fa-school fa-2x text-gray-300"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card border-left-warning">
              <div class="card-body">
                <div class="row no-gutters align-items-center">
                  <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                      Pending Fees
                    </div>
                    <div class="h4 mb-0 font-weight-bold text-gray-800">
                      ${{ stats?.pending_fees?.toFixed(2) || '0.00' }}
                    </div>
                  </div>
                  <div class="col-auto">
                    <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-3 mb-3">
                    <button class="btn btn-primary w-100" routerLink="/students">
                      <i class="fas fa-users me-2"></i>
                      View Students
                    </button>
                  </div>
                  <div class="col-md-3 mb-3">
                    <button class="btn btn-success w-100" routerLink="/fees">
                      <i class="fas fa-dollar-sign me-2"></i>
                      Manage Fees
                    </button>
                  </div>
                  <div class="col-md-3 mb-3">
                    <button class="btn btn-info w-100" routerLink="/exams">
                      <i class="fas fa-clipboard-list me-2"></i>
                      View Exams
                    </button>
                  </div>
                  <div class="col-md-3 mb-3">
                    <button class="btn btn-warning w-100" routerLink="/events">
                      <i class="fas fa-calendar-plus me-2"></i>
                      View Events
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="row">
          <div class="col-lg-8 mb-4">
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Students</h5>
                <a routerLink="/students" class="btn btn-sm btn-outline-primary">View All</a>
              </div>
              <div class="card-body">
                <div *ngIf="isLoading" class="text-center py-4">
                  <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                </div>
                <div *ngIf="!isLoading && recentStudents.length === 0" class="text-center py-4 text-muted">
                  No students found
                </div>
                <div *ngIf="!isLoading && recentStudents.length > 0" class="table-responsive">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th>Student ID</th>
                        <th>Name</th>
                        <th>Grade</th>
                        <th>Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let student of recentStudents">
                        <td>{{ student.student_id }}</td>
                        <td>{{ student.first_name }} {{ student.last_name }}</td>
                        <td>{{ student.current_enrollment?.school_class?.grade_level || 'N/A' }}</td>
                        <td>
                          <span class="badge" [ngClass]="{
                            'bg-success': student.status === 'active',
                            'bg-secondary': student.status === 'inactive',
                            'bg-primary': student.status === 'graduated',
                            'bg-warning': student.status === 'transferred'
                          }">
                            {{ student.status | titlecase }}
                          </span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <div class="col-lg-4 mb-4">
            <div class="card">
              <div class="card-header">
                <h5 class="mb-0">System Overview</h5>
              </div>
              <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                  <span>Upcoming Events</span>
                  <span class="badge bg-info">{{ stats?.upcoming_events || 0 }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                  <span>Upcoming Exams</span>
                  <span class="badge bg-warning">{{ stats?.upcoming_exams || 0 }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                  <span>Active Students</span>
                  <span class="badge bg-success">{{ stats?.total_students || 0 }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                  <span>System Status</span>
                  <span class="badge bg-success">Online</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </app-layout>
  `,
  styles: [`
    .dashboard-container {
      max-width: 1200px;
    }

    .stat-card {
      transition: all 0.3s ease;
      border: none;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .stat-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .border-left-primary {
      border-left: 4px solid #4f46e5 !important;
    }

    .border-left-success {
      border-left: 4px solid #10b981 !important;
    }

    .border-left-info {
      border-left: 4px solid #3b82f6 !important;
    }

    .border-left-warning {
      border-left: 4px solid #f59e0b !important;
    }

    .text-gray-300 {
      color: #d1d5db !important;
    }

    .text-gray-800 {
      color: #1f2937 !important;
    }

    .card {
      border: none;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .card-header {
      background: white;
      border-bottom: 1px solid #e5e7eb;
      border-radius: 12px 12px 0 0 !important;
    }

    .table th {
      border-top: none;
      font-weight: 600;
      color: #374151;
    }

    .btn {
      border-radius: 8px;
      font-weight: 500;
    }
  `]
})
export class DashboardComponent implements OnInit {
  private http = inject(HttpClient);
  private authService = inject(AuthService);

  stats: DashboardStats | null = null;
  recentStudents: any[] = [];
  isLoading = true;
  currentUser = this.authService.getCurrentUserValue();

  ngOnInit(): void {
    this.loadDashboardData();
  }

  private loadDashboardData(): void {
    // Load recent students
    this.http.get<any>(`${environment.apiUrl}/students?per_page=5`).subscribe({
      next: (response) => {
        this.recentStudents = response.data || [];
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading students:', error);
        this.isLoading = false;
      }
    });

    // Simulate stats (in real app, you'd have a dashboard API endpoint)
    this.stats = {
      total_students: 0,
      total_teachers: 0,
      total_classes: 0,
      pending_fees: 0,
      upcoming_events: 0,
      upcoming_exams: 0
    };
  }
}
