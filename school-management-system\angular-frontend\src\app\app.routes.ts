import { Routes } from '@angular/router';
import { authGuard } from './core/guards/auth.guard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },
  {
    path: 'login',
    loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent)
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent),
    canActivate: [authGuard]
  },
  {
    path: 'students',
    loadChildren: () => import('./features/students/students.routes').then(m => m.studentsRoutes),
    canActivate: [authGuard]
  },
  {
    path: 'fees',
    loadChildren: () => import('./features/fees/fees.routes').then(m => m.feesRoutes),
    canActivate: [authGuard]
  },
  {
    path: 'schedules',
    loadChildren: () => import('./features/schedules/schedules.routes').then(m => m.schedulesRoutes),
    canActivate: [authGuard]
  },
  {
    path: 'exams',
    loadChildren: () => import('./features/exams/exams.routes').then(m => m.examsRoutes),
    canActivate: [authGuard]
  },
  {
    path: 'events',
    loadChildren: () => import('./features/events/events.routes').then(m => m.eventsRoutes),
    canActivate: [authGuard]
  },
  {
    path: '**',
    redirectTo: '/dashboard'
  }
];
