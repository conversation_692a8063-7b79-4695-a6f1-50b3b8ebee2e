import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, RouterLink, RouterLinkActive, Router } from '@angular/router';
import { AuthService } from '../../../core/services/auth.service';
import { User } from '../../../core/models/user.model';

@Component({
  selector: 'app-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet, RouterLink, RouterLinkActive],
  template: `
    <div class="d-flex">
      <!-- Sidebar -->
      <nav class="sidebar d-flex flex-column p-3" [class.show]="sidebarOpen">
        <div class="sidebar-header mb-4">
          <h5 class="text-white mb-0">
            <i class="fas fa-school me-2"></i>
            School Management
          </h5>
        </div>

        <ul class="nav nav-pills flex-column mb-auto">
          <li class="nav-item">
            <a class="nav-link" routerLink="/dashboard" routerLinkActive="active">
              <i class="fas fa-tachometer-alt me-2"></i>
              Dashboard
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" routerLink="/students" routerLinkActive="active">
              <i class="fas fa-user-graduate me-2"></i>
              Students
            </a>
          </li>
          <li class="nav-item" *ngIf="canViewFees()">
            <a class="nav-link" routerLink="/fees" routerLinkActive="active">
              <i class="fas fa-dollar-sign me-2"></i>
              Tuition Fees
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" routerLink="/schedules" routerLinkActive="active">
              <i class="fas fa-calendar-alt me-2"></i>
              Schedules
            </a>
          </li>
          <li class="nav-item" *ngIf="canViewExams()">
            <a class="nav-link" routerLink="/exams" routerLinkActive="active">
              <i class="fas fa-clipboard-list me-2"></i>
              Exams
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" routerLink="/events" routerLinkActive="active">
              <i class="fas fa-calendar-check me-2"></i>
              Events
            </a>
          </li>
        </ul>

        <div class="user-info mt-auto">
          <div class="dropdown">
            <button class="btn btn-outline-light dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
              <i class="fas fa-user me-2"></i>
              {{ currentUser?.first_name }} {{ currentUser?.last_name }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
              <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" (click)="logout()"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
            </ul>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="main-content flex-grow-1">
        <!-- Top Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
          <div class="container-fluid">
            <button class="btn btn-outline-secondary d-md-none" (click)="toggleSidebar()">
              <i class="fas fa-bars"></i>
            </button>
            
            <div class="navbar-nav ms-auto">
              <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                  <i class="fas fa-bell"></i>
                  <span class="badge bg-danger">3</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                  <li><h6 class="dropdown-header">Notifications</h6></li>
                  <li><a class="dropdown-item" href="#">New student enrollment</a></li>
                  <li><a class="dropdown-item" href="#">Fee payment received</a></li>
                  <li><a class="dropdown-item" href="#">Exam results published</a></li>
                </ul>
              </div>
            </div>
          </div>
        </nav>

        <!-- Page Content -->
        <div class="content p-4">
          <router-outlet></router-outlet>
        </div>
      </div>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div class="sidebar-overlay d-md-none" 
         [class.show]="sidebarOpen" 
         (click)="closeSidebar()"></div>
  `,
  styles: [`
    .sidebar {
      width: 250px;
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      position: fixed;
      top: 0;
      left: 0;
      z-index: 1000;
      transition: all 0.3s ease;
    }

    .main-content {
      margin-left: 250px;
      min-height: 100vh;
      background: #f8f9fa;
    }

    .sidebar-header h5 {
      font-weight: 600;
    }

    .nav-link {
      color: rgba(255, 255, 255, 0.8) !important;
      padding: 12px 16px;
      margin: 2px 0;
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .nav-link:hover {
      color: white !important;
      background: rgba(255, 255, 255, 0.1);
      transform: translateX(5px);
    }

    .nav-link.active {
      color: white !important;
      background: rgba(255, 255, 255, 0.2);
      font-weight: 600;
    }

    .navbar {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .content {
      background: #f8f9fa;
    }

    .user-info {
      border-top: 1px solid rgba(255, 255, 255, 0.2);
      padding-top: 1rem;
    }

    .sidebar-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .sidebar-overlay.show {
      opacity: 1;
      visibility: visible;
    }

    @media (max-width: 768px) {
      .sidebar {
        left: -250px;
      }

      .sidebar.show {
        left: 0;
      }

      .main-content {
        margin-left: 0;
      }
    }
  `]
})
export class LayoutComponent {
  private authService = inject(AuthService);
  private router = inject(Router);

  currentUser: User | null = null;
  sidebarOpen = false;

  constructor() {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
  }

  toggleSidebar(): void {
    this.sidebarOpen = !this.sidebarOpen;
  }

  closeSidebar(): void {
    this.sidebarOpen = false;
  }

  canViewFees(): boolean {
    return this.authService.hasPermission('view fees') || this.authService.isAdmin();
  }

  canViewExams(): boolean {
    return this.authService.hasPermission('view exams') || this.authService.isAdmin() || this.authService.isTeacher();
  }

  logout(): void {
    this.authService.logout().subscribe({
      next: () => {
        this.router.navigate(['/login']);
      },
      error: () => {
        // Even if logout fails on server, clear local session
        localStorage.clear();
        this.router.navigate(['/login']);
      }
    });
  }
}
