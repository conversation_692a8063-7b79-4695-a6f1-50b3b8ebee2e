# School Management System - Angular Frontend

This is the Angular 20 frontend application for the School Management System that connects to the Laravel API.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Angular CLI 18+

### Installation

1. **Install Angular CLI globally:**
```bash
npm install -g @angular/cli@latest
```

2. **Navigate to the frontend directory:**
```bash
cd angular-frontend
```

3. **Install dependencies:**
```bash
npm install
```

4. **Start the development server:**
```bash
npm start
# or
ng serve
```

The application will be available at: **http://localhost:4200**

## 📋 Features

### ✅ Implemented
- **Authentication System** - Login/logout with JWT tokens
- **Dashboard** - Overview with statistics and quick actions
- **Student Management** - List, search, filter students
- **Responsive Layout** - Mobile-friendly sidebar navigation
- **Role-based UI** - Different views based on user permissions
- **Modern UI/UX** - Clean design with Bootstrap 5 and custom styling

### 🚧 In Progress
- Student detail view and editing
- Fee management interface
- Schedule management
- Exam management
- Event management
- Advanced filtering and sorting
- File upload for student photos
- Bulk operations

## 🏗️ Architecture

### Project Structure
```
src/
├── app/
│   ├── core/                    # Core functionality
│   │   ├── guards/             # Route guards
│   │   ├── interceptors/       # HTTP interceptors
│   │   ├── models/             # TypeScript interfaces
│   │   └── services/           # API services
│   ├── features/               # Feature modules
│   │   ├── auth/              # Authentication
│   │   ├── dashboard/         # Dashboard
│   │   ├── students/          # Student management
│   │   ├── fees/              # Fee management
│   │   ├── schedules/         # Schedule management
│   │   ├── exams/             # Exam management
│   │   └── events/            # Event management
│   ├── shared/                # Shared components
│   │   └── components/        # Reusable components
│   └── environments/          # Environment configs
```

### Key Technologies
- **Angular 18** - Latest Angular with standalone components
- **TypeScript** - Type-safe development
- **Bootstrap 5** - Responsive UI framework
- **RxJS** - Reactive programming
- **Font Awesome** - Icons
- **SCSS** - Enhanced styling

## 🔧 Configuration

### Environment Settings
Update `src/environments/environment.ts`:

```typescript
export const environment = {
  production: false,
  apiUrl: 'http://localhost:8000/api'  // Laravel API URL
};
```

### API Integration
The app connects to the Laravel API at `http://localhost:8000/api`. Make sure the Laravel server is running.

## 🎯 Usage

### Login Credentials
Use these demo credentials to test the application:

- **Admin:** <EMAIL> / password
- **Teacher:** <EMAIL> / password  
- **Staff:** <EMAIL> / password

### Navigation
- **Dashboard** - Overview and quick actions
- **Students** - Manage student records
- **Fees** - Handle tuition fees and payments
- **Schedules** - View and manage class schedules
- **Exams** - Manage exams and results
- **Events** - School events and activities

### Features by Role

#### Admin Users
- Full access to all modules
- Student CRUD operations
- Fee management
- System administration

#### Teacher Users
- View students in their classes
- Manage exams and results
- View schedules
- Limited administrative access

#### Staff Users
- View student information
- Process fee payments
- Basic reporting access

## 🔐 Security

### Authentication
- JWT token-based authentication
- Automatic token refresh
- Secure route protection
- Role-based access control

### HTTP Interceptors
- Automatic token attachment
- Error handling
- Request/response logging

## 📱 Responsive Design

The application is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones

### Mobile Features
- Collapsible sidebar navigation
- Touch-friendly interface
- Optimized layouts for small screens

## 🎨 UI/UX Features

### Modern Design
- Clean, professional interface
- Consistent color scheme
- Smooth animations and transitions
- Intuitive navigation

### User Experience
- Loading states and spinners
- Error handling and user feedback
- Search and filtering capabilities
- Pagination for large datasets

## 🛠️ Development

### Available Scripts
```bash
# Start development server
npm start

# Build for production
npm run build

# Run tests
npm test

# Lint code
ng lint

# Serve production build
npm run serve
```

### Code Style
- TypeScript strict mode enabled
- ESLint for code quality
- Prettier for code formatting
- Angular style guide compliance

### Component Architecture
- Standalone components (Angular 18+)
- Lazy loading for feature modules
- Reactive forms for user input
- OnPush change detection strategy

## 🚀 Deployment

### Production Build
```bash
ng build --configuration production
```

### Environment Configuration
Create production environment file:
```typescript
// src/environments/environment.prod.ts
export const environment = {
  production: true,
  apiUrl: 'https://your-api-domain.com/api'
};
```

## 🔄 API Integration

### Services
- **AuthService** - Authentication and user management
- **StudentService** - Student CRUD operations
- **HTTP Interceptors** - Automatic token handling

### Error Handling
- Global error interceptor
- User-friendly error messages
- Automatic retry for failed requests

## 📊 Performance

### Optimization Features
- Lazy loading of feature modules
- OnPush change detection
- Tree shaking for smaller bundles
- Service workers for caching (optional)

### Bundle Analysis
```bash
ng build --stats-json
npx webpack-bundle-analyzer dist/school-management-frontend/stats.json
```

## 🧪 Testing

### Unit Tests
```bash
ng test
```

### E2E Tests
```bash
ng e2e
```

## 📞 Support

### Common Issues

1. **CORS Errors**
   - Ensure Laravel API has CORS configured
   - Check API URL in environment files

2. **Authentication Issues**
   - Verify JWT token format
   - Check token expiration
   - Ensure API endpoints are correct

3. **Build Errors**
   - Clear node_modules and reinstall
   - Check Angular CLI version compatibility

### Getting Help
- Check the Laravel API documentation
- Review browser console for errors
- Verify network requests in DevTools

## 🎯 Next Steps

1. **Complete Student Management**
   - Add/edit student forms
   - Student detail views
   - Photo upload functionality

2. **Implement Remaining Modules**
   - Fee management interface
   - Schedule calendar view
   - Exam result entry
   - Event registration

3. **Advanced Features**
   - Real-time notifications
   - Advanced reporting
   - Data export functionality
   - Mobile app (Ionic/React Native)

## 📄 License

This project is part of the School Management System and follows the same licensing terms.
