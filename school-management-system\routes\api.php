<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\StudentController;
use App\Http\Controllers\Api\TuitionFeeController;
use App\Http\Controllers\Api\ScheduleController;
use App\Http\Controllers\Api\ExamController;
use App\Http\Controllers\Api\EventController;
use App\Http\Controllers\Api\YoutubeVideoController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Public routes
Route::post('/login', [AuthController::class, 'login']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Authentication routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);

    // Student management
    Route::apiResource('students', StudentController::class);

    // Tuition fees
    Route::apiResource('tuition-fees', TuitionFeeController::class);
    Route::get('students/{student}/tuition-fees', [TuitionFeeController::class, 'getStudentFees']);
    Route::post('tuition-fees/{fee}/pay', [TuitionFeeController::class, 'recordPayment']);

    // Schedules
    Route::apiResource('schedules', ScheduleController::class);
    Route::get('classes/{class}/schedules', [ScheduleController::class, 'getClassSchedules']);
    Route::get('students/{student}/schedules', [ScheduleController::class, 'getStudentSchedules']);

    // Exams
    Route::apiResource('exams', ExamController::class);
    Route::get('students/{student}/exams', [ExamController::class, 'getStudentExams']);
    Route::post('exams/{exam}/results', [ExamController::class, 'recordResults']);

    // Events
    Route::apiResource('events', EventController::class);
    Route::post('events/{event}/register', [EventController::class, 'registerStudent']);
    Route::get('students/{student}/events', [EventController::class, 'getStudentEvents']);

    // YouTube Videos
    Route::apiResource('youtube-videos', YoutubeVideoController::class);
    Route::get('youtube-videos/grade/{grade}', [YoutubeVideoController::class, 'getByGrade']);
    Route::post('youtube-videos/search', [YoutubeVideoController::class, 'searchVideos']);
});
