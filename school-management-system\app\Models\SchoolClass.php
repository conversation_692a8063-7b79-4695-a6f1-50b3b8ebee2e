<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SchoolClass extends Model
{
    use HasFactory;

    protected $table = 'classes';

    protected $fillable = [
        'name',
        'grade_level',
        'section',
        'academic_year_id',
        'class_teacher_id',
        'max_students',
        'room_number',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Academic year relationship.
     */
    public function academicYear()
    {
        return $this->belongsTo(AcademicYear::class);
    }

    /**
     * Class teacher relationship.
     */
    public function classTeacher()
    {
        return $this->belongsTo(User::class, 'class_teacher_id');
    }

    /**
     * Class enrollments.
     */
    public function enrollments()
    {
        return $this->hasMany(Enrollment::class, 'class_id');
    }

    /**
     * Students in this class.
     */
    public function students()
    {
        return $this->belongsToMany(Student::class, 'enrollments')
                    ->wherePivot('status', 'enrolled');
    }

    /**
     * Class schedules.
     */
    public function schedules()
    {
        return $this->hasMany(Schedule::class, 'class_id');
    }

    /**
     * Class exams.
     */
    public function exams()
    {
        return $this->hasMany(Exam::class, 'class_id');
    }

    /**
     * Get current student count.
     */
    public function getCurrentStudentCountAttribute()
    {
        return $this->enrollments()->where('status', 'enrolled')->count();
    }
}
