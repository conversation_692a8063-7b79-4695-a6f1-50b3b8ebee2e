<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use <PERSON><PERSON>\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Student management
            'view students',
            'create students',
            'edit students',
            'delete students',

            // Academic management
            'view classes',
            'create classes',
            'edit classes',
            'delete classes',

            // Fee management
            'view fees',
            'create fees',
            'edit fees',
            'delete fees',
            'record payments',

            // Schedule management
            'view schedules',
            'create schedules',
            'edit schedules',
            'delete schedules',

            // Exam management
            'view exams',
            'create exams',
            'edit exams',
            'delete exams',
            'record results',

            // Event management
            'view events',
            'create events',
            'edit events',
            'delete events',

            // User management
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Reports
            'view reports',
            'generate reports',

            // System settings
            'manage settings',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions
        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo(Permission::all());

        $teacherRole = Role::create(['name' => 'teacher']);
        $teacherRole->givePermissionTo([
            'view students',
            'view classes',
            'view schedules',
            'view exams',
            'create exams',
            'edit exams',
            'record results',
            'view events',
        ]);

        $staffRole = Role::create(['name' => 'staff']);
        $staffRole->givePermissionTo([
            'view students',
            'view classes',
            'view fees',
            'record payments',
            'view schedules',
            'view events',
        ]);
    }
}
