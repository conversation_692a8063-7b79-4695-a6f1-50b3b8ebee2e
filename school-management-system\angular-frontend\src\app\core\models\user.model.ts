export interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  user_type: 'admin' | 'teacher' | 'staff';
  employee_id?: string;
  address?: string;
  hire_date?: string;
  is_active: boolean;
  profile_photo?: string;
  roles?: Role[];
  permissions?: Permission[];
  full_name?: string;
  created_at: string;
  updated_at: string;
}

export interface Role {
  id: number;
  name: string;
  guard_name: string;
  created_at: string;
  updated_at: string;
}

export interface Permission {
  id: number;
  name: string;
  guard_name: string;
  created_at: string;
  updated_at: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  message: string;
}
