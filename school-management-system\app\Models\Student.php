<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Student extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'first_name',
        'last_name',
        'birth_date',
        'gender',
        'address',
        'phone',
        'email',
        'blood_type',
        'medical_conditions',
        'profile_photo',
        'primary_guardian_id',
        'secondary_guardian_id',
        'enrollment_date',
        'status',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'enrollment_date' => 'date',
    ];

    /**
     * Get the student's full name.
     */
    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Get the student's age.
     */
    public function getAgeAttribute()
    {
        return $this->birth_date->age;
    }

    /**
     * Primary guardian relationship.
     */
    public function primaryGuardian()
    {
        return $this->belongsTo(Guardian::class, 'primary_guardian_id');
    }

    /**
     * Secondary guardian relationship.
     */
    public function secondaryGuardian()
    {
        return $this->belongsTo(Guardian::class, 'secondary_guardian_id');
    }

    /**
     * Student enrollments.
     */
    public function enrollments()
    {
        return $this->hasMany(Enrollment::class);
    }

    /**
     * Current enrollment.
     */
    public function currentEnrollment()
    {
        return $this->hasOne(Enrollment::class)->where('status', 'enrolled');
    }

    /**
     * Tuition fees.
     */
    public function tuitionFees()
    {
        return $this->hasMany(TuitionFee::class);
    }

    /**
     * Student performances.
     */
    public function performances()
    {
        return $this->hasMany(StudentPerformance::class);
    }

    /**
     * Event registrations.
     */
    public function eventRegistrations()
    {
        return $this->hasMany(EventRegistration::class);
    }
}
