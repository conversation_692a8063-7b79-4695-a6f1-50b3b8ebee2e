import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  template: `
    <div class="login-container">
      <div class="login-card">
        <div class="login-header">
          <i class="fas fa-school fa-3x text-primary mb-3"></i>
          <h2>School Management System</h2>
          <p class="text-muted">Sign in to your account</p>
        </div>

        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
          <div class="mb-3">
            <label for="email" class="form-label">Email Address</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="fas fa-envelope"></i>
              </span>
              <input 
                type="email" 
                class="form-control" 
                id="email"
                formControlName="email"
                [class.is-invalid]="loginForm.get('email')?.invalid && loginForm.get('email')?.touched"
                placeholder="Enter your email">
            </div>
            <div class="invalid-feedback" *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">
              <div *ngIf="loginForm.get('email')?.errors?.['required']">Email is required</div>
              <div *ngIf="loginForm.get('email')?.errors?.['email']">Please enter a valid email</div>
            </div>
          </div>

          <div class="mb-3">
            <label for="password" class="form-label">Password</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="fas fa-lock"></i>
              </span>
              <input 
                [type]="showPassword ? 'text' : 'password'" 
                class="form-control" 
                id="password"
                formControlName="password"
                [class.is-invalid]="loginForm.get('password')?.invalid && loginForm.get('password')?.touched"
                placeholder="Enter your password">
              <button 
                type="button" 
                class="btn btn-outline-secondary"
                (click)="togglePassword()">
                <i [class]="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>
            <div class="invalid-feedback" *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
              <div *ngIf="loginForm.get('password')?.errors?.['required']">Password is required</div>
            </div>
          </div>

          <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="remember" formControlName="remember">
            <label class="form-check-label" for="remember">
              Remember me
            </label>
          </div>

          <div class="alert alert-danger" *ngIf="errorMessage">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ errorMessage }}
          </div>

          <button 
            type="submit" 
            class="btn btn-primary w-100 mb-3"
            [disabled]="loginForm.invalid || isLoading">
            <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2"></span>
            <i *ngIf="!isLoading" class="fas fa-sign-in-alt me-2"></i>
            {{ isLoading ? 'Signing in...' : 'Sign In' }}
          </button>
        </form>

        <div class="demo-credentials">
          <h6>Demo Credentials:</h6>
          <div class="credential-item" (click)="fillCredentials('admin')">
            <strong>Admin:</strong> <EMAIL> / password
          </div>
          <div class="credential-item" (click)="fillCredentials('teacher')">
            <strong>Teacher:</strong> <EMAIL> / password
          </div>
          <div class="credential-item" (click)="fillCredentials('staff')">
            <strong>Staff:</strong> <EMAIL> / password
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .login-container {
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }

    .login-card {
      background: white;
      border-radius: 20px;
      padding: 40px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 400px;
      backdrop-filter: blur(10px);
    }

    .login-header {
      text-align: center;
      margin-bottom: 30px;
    }

    .login-header h2 {
      color: #333;
      margin-bottom: 10px;
      font-weight: 600;
    }

    .input-group-text {
      background: #f8f9fa;
      border-color: #e9ecef;
    }

    .form-control {
      border-color: #e9ecef;
    }

    .form-control:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      padding: 12px;
      font-weight: 500;
    }

    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    .demo-credentials {
      margin-top: 20px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 10px;
      font-size: 0.9em;
    }

    .demo-credentials h6 {
      margin-bottom: 10px;
      color: #666;
    }

    .credential-item {
      cursor: pointer;
      padding: 5px;
      border-radius: 5px;
      margin-bottom: 5px;
      transition: background-color 0.2s;
    }

    .credential-item:hover {
      background: #e9ecef;
    }

    .alert {
      border-radius: 10px;
    }
  `]
})
export class LoginComponent {
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private router = inject(Router);

  loginForm: FormGroup;
  isLoading = false;
  errorMessage = '';
  showPassword = false;

  constructor() {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]],
      remember: [false]
    });

    // Redirect if already authenticated
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/dashboard']);
    }
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.isLoading = true;
      this.errorMessage = '';

      const credentials = {
        email: this.loginForm.value.email,
        password: this.loginForm.value.password
      };

      this.authService.login(credentials).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.router.navigate(['/dashboard']);
        },
        error: (error) => {
          this.isLoading = false;
          this.errorMessage = error.error?.message || 'Login failed. Please try again.';
        }
      });
    }
  }

  togglePassword(): void {
    this.showPassword = !this.showPassword;
  }

  fillCredentials(type: 'admin' | 'teacher' | 'staff'): void {
    const credentials = {
      admin: { email: '<EMAIL>', password: 'password' },
      teacher: { email: '<EMAIL>', password: 'password' },
      staff: { email: '<EMAIL>', password: 'password' }
    };

    this.loginForm.patchValue(credentials[type]);
  }
}
