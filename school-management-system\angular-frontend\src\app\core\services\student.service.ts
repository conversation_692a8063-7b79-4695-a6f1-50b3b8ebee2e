import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { 
  Student, 
  CreateStudentRequest, 
  UpdateStudentRequest 
} from '../models/student.model';
import { 
  ApiResponse, 
  PaginatedResponse, 
  QueryParams 
} from '../models/api-response.model';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class StudentService {
  private http = inject(HttpClient);
  private apiUrl = `${environment.apiUrl}/students`;

  getStudents(params?: QueryParams): Observable<PaginatedResponse<Student>> {
    let httpParams = new HttpParams();
    
    if (params) {
      Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
          httpParams = httpParams.set(key, params[key].toString());
        }
      });
    }

    return this.http.get<PaginatedResponse<Student>>(this.apiUrl, { params: httpParams });
  }

  getStudent(id: number): Observable<ApiResponse<Student>> {
    return this.http.get<ApiResponse<Student>>(`${this.apiUrl}/${id}`);
  }

  createStudent(student: CreateStudentRequest): Observable<ApiResponse<Student>> {
    return this.http.post<ApiResponse<Student>>(this.apiUrl, student);
  }

  updateStudent(id: number, student: UpdateStudentRequest): Observable<ApiResponse<Student>> {
    return this.http.put<ApiResponse<Student>>(`${this.apiUrl}/${id}`, student);
  }

  deleteStudent(id: number): Observable<ApiResponse<any>> {
    return this.http.delete<ApiResponse<any>>(`${this.apiUrl}/${id}`);
  }

  searchStudents(query: string): Observable<PaginatedResponse<Student>> {
    const params = new HttpParams().set('search', query);
    return this.http.get<PaginatedResponse<Student>>(this.apiUrl, { params });
  }

  getStudentsByStatus(status: string): Observable<PaginatedResponse<Student>> {
    const params = new HttpParams().set('status', status);
    return this.http.get<PaginatedResponse<Student>>(this.apiUrl, { params });
  }

  getStudentsByGrade(gradeLevel: string): Observable<PaginatedResponse<Student>> {
    const params = new HttpParams().set('grade_level', gradeLevel);
    return this.http.get<PaginatedResponse<Student>>(this.apiUrl, { params });
  }

  getStudentTuitionFees(studentId: number): Observable<any> {
    return this.http.get(`${environment.apiUrl}/students/${studentId}/tuition-fees`);
  }

  getStudentSchedules(studentId: number): Observable<any> {
    return this.http.get(`${environment.apiUrl}/students/${studentId}/schedules`);
  }

  getStudentExams(studentId: number): Observable<any> {
    return this.http.get(`${environment.apiUrl}/students/${studentId}/exams`);
  }

  getStudentEvents(studentId: number): Observable<any> {
    return this.http.get(`${environment.apiUrl}/students/${studentId}/events`);
  }
}
