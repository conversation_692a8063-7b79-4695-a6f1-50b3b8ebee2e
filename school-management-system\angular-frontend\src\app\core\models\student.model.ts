export interface Student {
  id: number;
  student_id: string;
  first_name: string;
  last_name: string;
  birth_date: string;
  gender: 'male' | 'female';
  address: string;
  phone?: string;
  email?: string;
  blood_type?: string;
  medical_conditions?: string;
  profile_photo?: string;
  primary_guardian_id: number;
  secondary_guardian_id?: number;
  enrollment_date: string;
  status: 'active' | 'inactive' | 'graduated' | 'transferred';
  primary_guardian?: Guardian;
  secondary_guardian?: Guardian;
  enrollments?: Enrollment[];
  current_enrollment?: Enrollment;
  tuition_fees?: TuitionFee[];
  performances?: StudentPerformance[];
  event_registrations?: EventRegistration[];
  full_name?: string;
  age?: number;
  created_at: string;
  updated_at: string;
}

export interface Guardian {
  id: number;
  first_name: string;
  last_name: string;
  relationship: string;
  phone?: string;
  email?: string;
  address?: string;
  occupation?: string;
  workplace?: string;
  emergency_contact?: string;
  created_at: string;
  updated_at: string;
}

export interface Enrollment {
  id: number;
  student_id: number;
  class_id: number;
  academic_year_id: number;
  enrollment_date: string;
  status: 'enrolled' | 'dropped' | 'completed';
  notes?: string;
  school_class?: SchoolClass;
  academic_year?: AcademicYear;
  created_at: string;
  updated_at: string;
}

export interface SchoolClass {
  id: number;
  name: string;
  grade_level: string;
  section: string;
  academic_year_id: number;
  class_teacher_id?: number;
  max_students: number;
  room_number?: string;
  is_active: boolean;
  academic_year?: AcademicYear;
  class_teacher?: any;
  current_student_count?: number;
  created_at: string;
  updated_at: string;
}

export interface AcademicYear {
  id: number;
  name: string;
  start_date: string;
  end_date: string;
  is_current: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface TuitionFee {
  id: number;
  student_id: number;
  academic_year_id: number;
  fee_type: string;
  amount: number;
  due_date: string;
  status: 'pending' | 'paid' | 'overdue' | 'partial';
  paid_amount: number;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface StudentPerformance {
  id: number;
  student_id: number;
  exam_id: number;
  marks_obtained: number;
  percentage: number;
  grade?: string;
  remarks?: string;
  is_absent: boolean;
  exam?: Exam;
  created_at: string;
  updated_at: string;
}

export interface Exam {
  id: number;
  title: string;
  class_id: number;
  subject_id: number;
  academic_year_id: number;
  exam_type: 'monthly' | 'midterm' | 'final' | 'quiz' | 'assignment';
  exam_date: string;
  start_time: string;
  end_time: string;
  total_marks: number;
  instructions?: string;
  room_number?: string;
  is_published: boolean;
  school_class?: SchoolClass;
  subject?: Subject;
  created_at: string;
  updated_at: string;
}

export interface Subject {
  id: number;
  name: string;
  code: string;
  description?: string;
  credits: number;
  grade_level: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface EventRegistration {
  id: number;
  event_id: number;
  student_id: number;
  registration_date: string;
  status: 'registered' | 'approved' | 'rejected' | 'cancelled';
  parent_approval: boolean;
  parent_approval_date?: string;
  parent_notes?: string;
  payment_required: boolean;
  payment_completed: boolean;
  event?: Event;
  created_at: string;
  updated_at: string;
}

export interface Event {
  id: number;
  title: string;
  description: string;
  event_type: 'trip' | 'sports' | 'cultural' | 'academic' | 'meeting' | 'holiday';
  event_date: string;
  start_time?: string;
  end_time?: string;
  location?: string;
  requires_parent_approval: boolean;
  cost?: number;
  registration_deadline?: string;
  requirements?: string;
  is_published: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateStudentRequest {
  student_id: string;
  first_name: string;
  last_name: string;
  birth_date: string;
  gender: 'male' | 'female';
  address: string;
  phone?: string;
  email?: string;
  blood_type?: string;
  medical_conditions?: string;
  primary_guardian_id: number;
  secondary_guardian_id?: number;
  enrollment_date: string;
}

export interface UpdateStudentRequest {
  student_id?: string;
  first_name?: string;
  last_name?: string;
  birth_date?: string;
  gender?: 'male' | 'female';
  address?: string;
  phone?: string;
  email?: string;
  blood_type?: string;
  medical_conditions?: string;
  primary_guardian_id?: number;
  secondary_guardian_id?: number;
  status?: 'active' | 'inactive' | 'graduated' | 'transferred';
}
