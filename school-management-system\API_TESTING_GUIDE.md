# School Management System API Testing Guide

## Quick Start

1. **Start the Laravel Server:**
   ```bash
   cd school-management-system
   php artisan serve
   ```
   The API will be available at: `http://localhost:8000`

2. **Admin Panel Access:**
   - URL: `http://localhost:8000/admin/login`
   - Email: `<EMAIL>`
   - Password: `password`

## API Testing Methods

### Method 1: Using cURL Commands

#### 1. Login to get API token
```bash
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password"
  }'
```

#### 2. Get authenticated user info
```bash
curl -X GET http://localhost:8000/api/user \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json"
```

#### 3. Get all students
```bash
curl -X GET http://localhost:8000/api/students \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json"
```

#### 4. Create a new student (requires guardian first)
```bash
# First create a guardian
curl -X POST http://localhost:8000/api/guardians \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "first_name": "John",
    "last_name": "Doe",
    "relationship": "father",
    "phone": "+1234567890",
    "email": "<EMAIL>",
    "address": "123 Main Street"
  }'

# Then create student
curl -X POST http://localhost:8000/api/students \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "student_id": "STU001",
    "first_name": "Jane",
    "last_name": "Doe",
    "birth_date": "2010-05-15",
    "gender": "female",
    "address": "123 Main Street",
    "phone": "+1234567891",
    "email": "<EMAIL>",
    "primary_guardian_id": 1,
    "enrollment_date": "2024-01-15"
  }'
```

### Method 2: Using Postman

#### Postman Collection Setup:

1. **Create a new collection** called "School Management API"

2. **Set up environment variables:**
   - `base_url`: `http://localhost:8000`
   - `token`: (will be set after login)

3. **Authentication Request:**
   - Method: POST
   - URL: `{{base_url}}/api/login`
   - Body (JSON):
   ```json
   {
     "email": "<EMAIL>",
     "password": "password"
   }
   ```
   - Test Script:
   ```javascript
   if (pm.response.code === 200) {
     const response = pm.response.json();
     pm.environment.set("token", response.token);
   }
   ```

4. **Students CRUD Operations:**

   **GET Students:**
   - Method: GET
   - URL: `{{base_url}}/api/students`
   - Headers: `Authorization: Bearer {{token}}`

   **POST Create Student:**
   - Method: POST
   - URL: `{{base_url}}/api/students`
   - Headers: `Authorization: Bearer {{token}}`
   - Body (JSON):
   ```json
   {
     "student_id": "STU002",
     "first_name": "John",
     "last_name": "Smith",
     "birth_date": "2011-03-20",
     "gender": "male",
     "address": "456 Oak Avenue",
     "primary_guardian_id": 1,
     "enrollment_date": "2024-01-15"
   }
   ```

### Method 3: Using Insomnia

1. **Create new request collection**
2. **Set base environment:** `http://localhost:8000`
3. **Create authentication request** (same as Postman)
4. **Use the token in subsequent requests**

### Method 4: Using PHP/Laravel Testing

Create test files in `tests/Feature/`:

```php
<?php
// tests/Feature/StudentApiTest.php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Student;
use App\Models\Guardian;
use Illuminate\Foundation\Testing\RefreshDatabase;

class StudentApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_can_login_and_get_token()
    {
        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure(['user', 'token', 'message']);
    }

    public function test_can_get_students_with_token()
    {
        $user = User::where('email', '<EMAIL>')->first();
        $token = $user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/students');

        $response->assertStatus(200);
    }
}
```

Run tests with:
```bash
php artisan test --filter StudentApiTest
```

## API Endpoints Reference

### Authentication
- `POST /api/login` - Login and get token
- `POST /api/logout` - Logout (requires token)
- `GET /api/user` - Get authenticated user info

### Students
- `GET /api/students` - List all students
- `POST /api/students` - Create new student
- `GET /api/students/{id}` - Get specific student
- `PUT /api/students/{id}` - Update student
- `DELETE /api/students/{id}` - Delete student

### Tuition Fees
- `GET /api/tuition-fees` - List all fees
- `POST /api/tuition-fees` - Create new fee
- `GET /api/students/{student}/tuition-fees` - Get student's fees
- `POST /api/tuition-fees/{fee}/pay` - Record payment

### Schedules
- `GET /api/schedules` - List all schedules
- `GET /api/classes/{class}/schedules` - Get class schedules
- `GET /api/students/{student}/schedules` - Get student schedules

### Exams
- `GET /api/exams` - List all exams
- `POST /api/exams` - Create new exam
- `GET /api/students/{student}/exams` - Get student exams
- `POST /api/exams/{exam}/results` - Record exam results

### Events
- `GET /api/events` - List all events
- `POST /api/events` - Create new event
- `POST /api/events/{event}/register` - Register student for event
- `GET /api/students/{student}/events` - Get student events

### YouTube Videos
- `GET /api/youtube-videos` - List all videos
- `GET /api/youtube-videos/grade/{grade}` - Get videos by grade
- `POST /api/youtube-videos/search` - Search videos

## Sample Data for Testing

### Guardian Data:
```json
{
  "first_name": "Michael",
  "last_name": "Johnson",
  "relationship": "father",
  "phone": "+1234567890",
  "email": "<EMAIL>",
  "address": "789 Pine Street",
  "occupation": "Engineer",
  "workplace": "Tech Corp"
}
```

### Student Data:
```json
{
  "student_id": "STU003",
  "first_name": "Emily",
  "last_name": "Johnson",
  "birth_date": "2012-07-10",
  "gender": "female",
  "address": "789 Pine Street",
  "phone": "+1234567892",
  "email": "<EMAIL>",
  "blood_type": "O+",
  "primary_guardian_id": 1,
  "enrollment_date": "2024-01-15"
}
```

## Error Handling

The API returns standard HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Server Error

Error responses include:
```json
{
  "message": "Error description",
  "errors": {
    "field": ["Validation error message"]
  }
}
```
