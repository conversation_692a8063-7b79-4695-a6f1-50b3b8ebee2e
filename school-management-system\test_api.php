<?php
/**
 * Simple API Test Script
 * Run this script to test the School Management System API
 * 
 * Usage: php test_api.php
 */

$baseUrl = 'http://localhost:8000';
$email = '<EMAIL>';
$password = 'password';

function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'status' => $httpCode,
        'body' => json_decode($response, true),
        'raw' => $response
    ];
}

function testEndpoint($name, $url, $method = 'GET', $data = null, $headers = []) {
    echo "\n🧪 Testing: $name\n";
    echo "   URL: $url\n";
    echo "   Method: $method\n";
    
    $result = makeRequest($url, $method, $data, $headers);
    
    if ($result['status'] >= 200 && $result['status'] < 300) {
        echo "   ✅ Status: {$result['status']} - SUCCESS\n";
        if (isset($result['body']['message'])) {
            echo "   📝 Message: {$result['body']['message']}\n";
        }
        return $result;
    } else {
        echo "   ❌ Status: {$result['status']} - FAILED\n";
        echo "   📝 Response: {$result['raw']}\n";
        return false;
    }
}

echo "🚀 Starting School Management System API Tests\n";
echo "================================================\n";

// Test 1: Login
$loginResult = testEndpoint(
    'User Login',
    "$baseUrl/api/login",
    'POST',
    ['email' => $email, 'password' => $password],
    ['Content-Type: application/json']
);

if (!$loginResult) {
    echo "\n❌ Login failed. Cannot continue with other tests.\n";
    echo "Make sure the server is running: php artisan serve\n";
    exit(1);
}

$token = $loginResult['body']['token'] ?? null;
if (!$token) {
    echo "\n❌ No token received from login. Cannot continue.\n";
    exit(1);
}

echo "   🔑 Token received: " . substr($token, 0, 20) . "...\n";

$authHeaders = [
    'Content-Type: application/json',
    "Authorization: Bearer $token"
];

// Test 2: Get User Info
testEndpoint(
    'Get User Info',
    "$baseUrl/api/user",
    'GET',
    null,
    $authHeaders
);

// Test 3: Get Students
testEndpoint(
    'Get All Students',
    "$baseUrl/api/students",
    'GET',
    null,
    $authHeaders
);

// Test 4: Get Tuition Fees
testEndpoint(
    'Get Tuition Fees',
    "$baseUrl/api/tuition-fees",
    'GET',
    null,
    $authHeaders
);

// Test 5: Get Schedules
testEndpoint(
    'Get Schedules',
    "$baseUrl/api/schedules",
    'GET',
    null,
    $authHeaders
);

// Test 6: Get Exams
testEndpoint(
    'Get Exams',
    "$baseUrl/api/exams",
    'GET',
    null,
    $authHeaders
);

// Test 7: Get Events
testEndpoint(
    'Get Events',
    "$baseUrl/api/events",
    'GET',
    null,
    $authHeaders
);

// Test 8: Get YouTube Videos
testEndpoint(
    'Get YouTube Videos',
    "$baseUrl/api/youtube-videos",
    'GET',
    null,
    $authHeaders
);

// Test 9: Logout
testEndpoint(
    'User Logout',
    "$baseUrl/api/logout",
    'POST',
    null,
    $authHeaders
);

echo "\n🎉 API Testing Complete!\n";
echo "================================================\n";
echo "📊 Summary:\n";
echo "   - All basic endpoints tested\n";
echo "   - Authentication working\n";
echo "   - API is ready for use\n\n";

echo "📚 Next Steps:\n";
echo "   1. Import the Postman collection: School_Management_API.postman_collection.json\n";
echo "   2. Access admin panel: http://localhost:8000/admin/login\n";
echo "   3. Read the API_TESTING_GUIDE.md for detailed testing instructions\n\n";

echo "🔗 Useful URLs:\n";
echo "   - API Base: $baseUrl/api\n";
echo "   - Admin Panel: $baseUrl/admin/login\n";
echo "   - Main Site: $baseUrl\n\n";
?>
