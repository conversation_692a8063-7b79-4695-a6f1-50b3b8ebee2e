/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: #f8f9fa;
  color: #333;
  line-height: 1.6;
}

/* Custom Bootstrap Variables */
:root {
  --bs-primary: #4f46e5;
  --bs-secondary: #6b7280;
  --bs-success: #10b981;
  --bs-danger: #ef4444;
  --bs-warning: #f59e0b;
  --bs-info: #3b82f6;
  --bs-light: #f8fafc;
  --bs-dark: #1f2937;
}

/* Custom Components */
.sidebar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: 12px 20px;
  margin: 4px 0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

.sidebar .nav-link.active {
  color: white;
  background: rgba(255, 255, 255, 0.2);
  font-weight: 600;
}

.main-content {
  background: #f8f9fa;
  min-height: 100vh;
}

/* Cards */
.card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  border-radius: 12px 12px 0 0 !important;
  padding: 20px;
}

/* Buttons */
.btn {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Forms */
.form-control {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  padding: 12px 16px;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Tables */
.table {
  border-radius: 12px;
  overflow: hidden;
}

.table thead th {
  background: #f8f9fa;
  border: none;
  font-weight: 600;
  color: #374151;
  padding: 16px;
}

.table tbody td {
  padding: 16px;
  border-color: #f3f4f6;
}

/* Badges */
.badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 500;
}

/* Loading Spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: -250px;
    width: 250px;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s ease;
  }
  
  .sidebar.show {
    left: 0;
  }
  
  .main-content {
    margin-left: 0 !important;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
