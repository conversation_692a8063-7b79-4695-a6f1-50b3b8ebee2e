<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('classes', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // e.g., "Grade 1-A"
            $table->string('grade_level'); // e.g., "Grade 1"
            $table->string('section'); // e.g., "A"
            $table->foreignId('academic_year_id')->constrained('academic_years');
            $table->foreignId('class_teacher_id')->nullable()->constrained('users');
            $table->integer('max_students')->default(30);
            $table->string('room_number')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('classes');
    }
};
