<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Student;
use App\Models\User;
use App\Models\SchoolClass;
use App\Models\TuitionFee;
use App\Models\Event;
use App\Models\Exam;

class DashboardController extends Controller
{
    public function index()
    {
        // Get dashboard statistics
        $stats = [
            'total_students' => Student::where('status', 'active')->count(),
            'total_teachers' => User::where('user_type', 'teacher')->where('is_active', true)->count(),
            'total_classes' => SchoolClass::where('is_active', true)->count(),
            'pending_fees' => TuitionFee::where('status', 'pending')->sum('amount'),
            'upcoming_events' => Event::where('event_date', '>=', now())->where('is_published', true)->count(),
            'upcoming_exams' => Exam::where('exam_date', '>=', now())->where('is_published', true)->count(),
        ];

        // Get recent activities
        $recent_students = Student::with('primaryGuardian')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        $upcoming_events = Event::where('event_date', '>=', now())
            ->where('is_published', true)
            ->orderBy('event_date')
            ->limit(5)
            ->get();

        $upcoming_exams = Exam::with(['schoolClass', 'subject'])
            ->where('exam_date', '>=', now())
            ->where('is_published', true)
            ->orderBy('exam_date')
            ->limit(5)
            ->get();

        return view('admin.dashboard', compact('stats', 'recent_students', 'upcoming_events', 'upcoming_exams'));
    }
}
