import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LayoutComponent } from '../../../shared/components/layout/layout.component';

@Component({
  selector: 'app-event-list',
  standalone: true,
  imports: [CommonModule, LayoutComponent],
  template: `
    <app-layout>
      <div class="container">
        <h2>Events</h2>
        <p>Event management component - Coming soon!</p>
      </div>
    </app-layout>
  `
})
export class EventListComponent {
}
