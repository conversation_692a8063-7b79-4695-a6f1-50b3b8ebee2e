{"info": {"name": "School Management System API", "description": "Complete API collection for School Management System", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('token', response.token);", "    pm.collectionVariables.set('token', response.token);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password\"\n}"}, "url": {"raw": "{{base_url}}/api/login", "host": ["{{base_url}}"], "path": ["api", "login"]}}}, {"name": "Get User Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/user", "host": ["{{base_url}}"], "path": ["api", "user"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/logout", "host": ["{{base_url}}"], "path": ["api", "logout"]}}}]}, {"name": "Students", "item": [{"name": "Get All Students", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/students", "host": ["{{base_url}}"], "path": ["api", "students"], "query": [{"key": "page", "value": "1", "disabled": true}, {"key": "per_page", "value": "15", "disabled": true}, {"key": "search", "value": "", "disabled": true}]}}}, {"name": "Create Student", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"student_id\": \"STU001\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"birth_date\": \"2010-05-15\",\n  \"gender\": \"male\",\n  \"address\": \"123 Main Street\",\n  \"phone\": \"+1234567890\",\n  \"email\": \"<EMAIL>\",\n  \"primary_guardian_id\": 1,\n  \"enrollment_date\": \"2024-01-15\"\n}"}, "url": {"raw": "{{base_url}}/api/students", "host": ["{{base_url}}"], "path": ["api", "students"]}}}, {"name": "Get Student by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/students/1", "host": ["{{base_url}}"], "path": ["api", "students", "1"]}}}, {"name": "Update Student", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON>\",\n  \"address\": \"456 Oak Avenue\"\n}"}, "url": {"raw": "{{base_url}}/api/students/1", "host": ["{{base_url}}"], "path": ["api", "students", "1"]}}}, {"name": "Delete Student", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/students/1", "host": ["{{base_url}}"], "path": ["api", "students", "1"]}}}]}, {"name": "<PERSON><PERSON>es", "item": [{"name": "Get All Fees", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/tuition-fees", "host": ["{{base_url}}"], "path": ["api", "tuition-fees"]}}}, {"name": "Get Student Fees", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/students/1/tuition-fees", "host": ["{{base_url}}"], "path": ["api", "students", "1", "tuition-fees"]}}}, {"name": "Create Fee", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"student_id\": 1,\n  \"academic_year_id\": 1,\n  \"fee_type\": \"tuition\",\n  \"amount\": 1500.00,\n  \"due_date\": \"2024-02-15\",\n  \"description\": \"Monthly tuition fee\"\n}"}, "url": {"raw": "{{base_url}}/api/tuition-fees", "host": ["{{base_url}}"], "path": ["api", "tuition-fees"]}}}]}, {"name": "Schedules", "item": [{"name": "Get All Schedules", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/schedules", "host": ["{{base_url}}"], "path": ["api", "schedules"]}}}, {"name": "Get Student Schedules", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/students/1/schedules", "host": ["{{base_url}}"], "path": ["api", "students", "1", "schedules"]}}}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "Get All Exams", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/exams", "host": ["{{base_url}}"], "path": ["api", "exams"]}}}, {"name": "Get Student Exams", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/students/1/exams", "host": ["{{base_url}}"], "path": ["api", "students", "1", "exams"]}}}]}, {"name": "Events", "item": [{"name": "Get All Events", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/events", "host": ["{{base_url}}"], "path": ["api", "events"]}}}, {"name": "Get Student Events", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/students/1/events", "host": ["{{base_url}}"], "path": ["api", "students", "1", "events"]}}}]}]}