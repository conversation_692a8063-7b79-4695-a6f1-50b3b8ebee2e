<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create admin user
        $admin = User::create([
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'admin',
            'employee_id' => 'EMP001',
            'phone' => '+1234567890',
            'address' => '123 School Street',
            'hire_date' => now(),
            'is_active' => true,
        ]);

        $admin->assignRole('admin');

        // Create a teacher user
        $teacher = User::create([
            'first_name' => 'John',
            'last_name' => 'Teacher',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'teacher',
            'employee_id' => 'EMP002',
            'phone' => '+1234567891',
            'address' => '456 Teacher Lane',
            'hire_date' => now(),
            'is_active' => true,
        ]);

        $teacher->assignRole('teacher');

        // Create a staff user
        $staff = User::create([
            'first_name' => 'Jane',
            'last_name' => 'Staff',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'staff',
            'employee_id' => 'EMP003',
            'phone' => '+1234567892',
            'address' => '789 Staff Avenue',
            'hire_date' => now(),
            'is_active' => true,
        ]);

        $staff->assignRole('staff');
    }
}
