@echo off
echo ========================================
echo School Management System - Angular Setup
echo ========================================
echo.

echo Checking if Node.js is installed...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js is installed: 
node --version

echo.
echo Checking if Angular CLI is installed...
ng version >nul 2>&1
if %errorlevel% neq 0 (
    echo Angular CLI not found. Installing globally...
    npm install -g @angular/cli@latest
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install Angular CLI
        pause
        exit /b 1
    )
)

echo Angular CLI is available

echo.
echo Navigating to Angular frontend directory...
cd angular-frontend

echo.
echo Installing npm dependencies...
npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Starting Angular development server...
echo The application will be available at: http://localhost:4200
echo.
echo Login credentials:
echo - Admin: <EMAIL> / password
echo - Teacher: <EMAIL> / password
echo - Staff: <EMAIL> / password
echo.
echo Make sure Laravel API is running at: http://localhost:8000
echo.
echo Press Ctrl+C to stop the server
echo ========================================

ng serve --open
