# School Management System - Quick Start Guide

## 🚀 Getting Started

### 1. Start the Application
```bash
cd school-management-system
php artisan serve
```
The application will be available at: **http://localhost:8000**

### 2. Access Admin Panel
- **URL:** http://localhost:8000/admin/login
- **Email:** <EMAIL>
- **Password:** password

### 3. Test the API

#### Option A: Using the Test Script
```bash
php test_api.php
```

#### Option B: Using cURL
```bash
# Login to get token
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Use the token from response in subsequent requests
curl -X GET http://localhost:8000/api/students \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### Option C: Import Postman Collection
1. Open Postman
2. Import `School_Management_API.postman_collection.json`
3. Set environment variable `base_url` to `http://localhost:8000`
4. Run the "Login" request first to get the token
5. Test other endpoints

## 📋 What's Included

### ✅ Database & Models
- **Students** - Complete student management with guardian info
- **Users** - Admin, teachers, staff with role-based permissions
- **Classes** - Grade levels, sections, academic years
- **Subjects** - Course management
- **Schedules** - Weekly timetables
- **Exams** - Test management and results
- **Events** - School events and trips
- **Tuition Fees** - Fee management and payments
- **YouTube Videos** - Educational content integration

### ✅ RESTful API
- **Authentication** - JWT token-based auth with Laravel Sanctum
- **CRUD Operations** - Full Create, Read, Update, Delete for all entities
- **Search & Filtering** - Advanced query capabilities
- **Pagination** - Efficient data loading
- **Validation** - Comprehensive input validation
- **Error Handling** - Proper HTTP status codes and messages

### ✅ Admin Panel
- **Dashboard** - Statistics and overview
- **Student Management** - Complete CRUD interface
- **Role-based Access** - Admin, Teacher, Staff permissions
- **Clean UI** - Bootstrap-based responsive design

### ✅ API Documentation
- **Swagger Annotations** - OpenAPI documentation
- **Postman Collection** - Ready-to-use API tests
- **Testing Guide** - Comprehensive testing instructions

## 🔧 API Endpoints Summary

### Authentication
- `POST /api/login` - Login and get JWT token
- `POST /api/logout` - Logout
- `GET /api/user` - Get authenticated user info

### Core Resources
- `GET|POST /api/students` - List/Create students
- `GET|PUT|DELETE /api/students/{id}` - Get/Update/Delete student
- `GET|POST /api/tuition-fees` - Fee management
- `GET|POST /api/schedules` - Schedule management
- `GET|POST /api/exams` - Exam management
- `GET|POST /api/events` - Event management
- `GET|POST /api/youtube-videos` - Video content

### Special Endpoints
- `GET /api/students/{id}/tuition-fees` - Get student's fees
- `POST /api/tuition-fees/{id}/pay` - Record payment
- `GET /api/students/{id}/schedules` - Get student's schedule
- `GET /api/students/{id}/exams` - Get student's exams
- `POST /api/events/{id}/register` - Register for event

## 🎯 Testing Scenarios

### 1. Basic Authentication Flow
1. Login with admin credentials
2. Get user info
3. Access protected endpoints
4. Logout

### 2. Student Management
1. Create a guardian first
2. Create a student with guardian reference
3. Update student information
4. Search and filter students
5. Get student details with relationships

### 3. Academic Operations
1. Create academic year
2. Create subjects and classes
3. Enroll students in classes
4. Create schedules
5. Create and manage exams

### 4. Financial Management
1. Create tuition fees for students
2. Record payments
3. Track pending fees
4. Generate fee reports

## 🔐 Default Users

The system comes with pre-seeded users:

### Admin User
- **Email:** <EMAIL>
- **Password:** password
- **Permissions:** Full system access

### Teacher User
- **Email:** <EMAIL>
- **Password:** password
- **Permissions:** Student viewing, exam management

### Staff User
- **Email:** <EMAIL>
- **Password:** password
- **Permissions:** Student viewing, fee management

## 📱 Features Highlights

### Student Management
- Complete student profiles with photos
- Guardian information and contacts
- Medical information and emergency contacts
- Academic history and performance tracking

### Academic Management
- Grade levels and class sections
- Subject management with credits
- Teacher assignments
- Academic year management

### Schedule Management
- Weekly timetables
- Room assignments
- Teacher schedules
- Student schedules

### Fee Management
- Multiple fee types (tuition, books, uniform, etc.)
- Payment tracking
- Due date management
- Late fee alerts

### Exam Management
- Multiple exam types (monthly, midterm, final)
- Result recording
- Performance analytics
- Grade calculations

### Event Management
- School events and trips
- Parent approval system
- Registration management
- Cost tracking

### YouTube Integration
- Educational video recommendations
- Grade-level filtering
- Subject-based categorization
- View tracking

## 🛠️ Technical Stack

- **Backend:** Laravel 9 (PHP)
- **Database:** MySQL
- **Authentication:** Laravel Sanctum (JWT)
- **Authorization:** Spatie Laravel Permission
- **API Documentation:** L5 Swagger
- **Frontend:** Bootstrap 5, Blade Templates
- **Icons:** Font Awesome
- **External APIs:** YouTube Data API

## 📞 Support

For questions or issues:
1. Check the `API_TESTING_GUIDE.md` for detailed testing instructions
2. Review the Postman collection for API examples
3. Check Laravel logs in `storage/logs/laravel.log`
4. Ensure database is properly migrated and seeded

## 🎉 Next Steps

1. **Customize the system** - Add your school's specific requirements
2. **Extend the API** - Add more endpoints as needed
3. **Improve the UI** - Enhance the admin panel design
4. **Add mobile app** - Use the API to build mobile applications
5. **Deploy to production** - Set up proper hosting and security
