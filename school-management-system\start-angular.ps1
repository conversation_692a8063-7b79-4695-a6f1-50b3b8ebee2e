Write-Host "========================================" -ForegroundColor Cyan
Write-Host "School Management System - Angular Setup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Checking if Node.js is installed..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "Node.js is installed: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Node.js is not installed!" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Checking if Angular CLI is installed..." -ForegroundColor Yellow
try {
    ng version | Out-Null
    Write-Host "Angular CLI is available" -ForegroundColor Green
} catch {
    Write-Host "Angular CLI not found. Installing globally..." -ForegroundColor Yellow
    npm install -g @angular/cli@latest
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Failed to install Angular CLI" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host ""
Write-Host "Navigating to Angular frontend directory..." -ForegroundColor Yellow
Set-Location angular-frontend

Write-Host ""
Write-Host "Installing npm dependencies..." -ForegroundColor Yellow
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to install dependencies" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Setup Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Starting Angular development server..." -ForegroundColor Yellow
Write-Host "The application will be available at: http://localhost:4200" -ForegroundColor Cyan
Write-Host ""
Write-Host "Login credentials:" -ForegroundColor White
Write-Host "- Admin: <EMAIL> / password" -ForegroundColor White
Write-Host "- Teacher: <EMAIL> / password" -ForegroundColor White
Write-Host "- Staff: <EMAIL> / password" -ForegroundColor White
Write-Host ""
Write-Host "Make sure Laravel API is running at: http://localhost:8000" -ForegroundColor Yellow
Write-Host ""
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Green

ng serve --open
