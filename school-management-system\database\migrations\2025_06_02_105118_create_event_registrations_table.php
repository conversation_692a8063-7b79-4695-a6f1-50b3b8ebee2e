<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('event_registrations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_id')->constrained('events');
            $table->foreignId('student_id')->constrained('students');
            $table->date('registration_date');
            $table->enum('status', ['registered', 'approved', 'rejected', 'cancelled'])->default('registered');
            $table->boolean('parent_approval')->default(false);
            $table->date('parent_approval_date')->nullable();
            $table->text('parent_notes')->nullable();
            $table->boolean('payment_required')->default(false);
            $table->boolean('payment_completed')->default(false);
            $table->timestamps();

            $table->unique(['event_id', 'student_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('event_registrations');
    }
};
