import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { LayoutComponent } from '../../../shared/components/layout/layout.component';
import { StudentService } from '../../../core/services/student.service';
import { Student } from '../../../core/models/student.model';
import { PaginatedResponse, QueryParams } from '../../../core/models/api-response.model';

@Component({
  selector: 'app-student-list',
  standalone: true,
  imports: [CommonModule, RouterLink, FormsModule, LayoutComponent],
  template: `
    <app-layout>
      <div class="student-list-container fade-in">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h1 class="h3 mb-0">Students</h1>
          <button class="btn btn-primary" routerLink="/students/new">
            <i class="fas fa-plus me-2"></i>
            Add Student
          </button>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
          <div class="card-body">
            <div class="row">
              <div class="col-md-4 mb-3">
                <label class="form-label">Search</label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="fas fa-search"></i>
                  </span>
                  <input 
                    type="text" 
                    class="form-control" 
                    placeholder="Search students..."
                    [(ngModel)]="searchTerm"
                    (keyup.enter)="search()">
                </div>
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Status</label>
                <select class="form-select" [(ngModel)]="selectedStatus" (change)="filterByStatus()">
                  <option value="">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="graduated">Graduated</option>
                  <option value="transferred">Transferred</option>
                </select>
              </div>
              <div class="col-md-3 mb-3">
                <label class="form-label">Grade Level</label>
                <select class="form-select" [(ngModel)]="selectedGrade" (change)="filterByGrade()">
                  <option value="">All Grades</option>
                  <option value="Grade 1">Grade 1</option>
                  <option value="Grade 2">Grade 2</option>
                  <option value="Grade 3">Grade 3</option>
                  <option value="Grade 4">Grade 4</option>
                  <option value="Grade 5">Grade 5</option>
                  <option value="Grade 6">Grade 6</option>
                </select>
              </div>
              <div class="col-md-2 mb-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                  <button class="btn btn-outline-secondary" (click)="clearFilters()">
                    <i class="fas fa-times me-2"></i>
                    Clear
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Students Table -->
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Student List</h5>
            <span class="badge bg-primary">{{ totalStudents }} Total</span>
          </div>
          <div class="card-body">
            <div *ngIf="isLoading" class="text-center py-4">
              <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>

            <div *ngIf="!isLoading && students.length === 0" class="text-center py-4">
              <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">No students found</h5>
              <p class="text-muted">Try adjusting your search criteria or add a new student.</p>
            </div>

            <div *ngIf="!isLoading && students.length > 0" class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Student ID</th>
                    <th>Name</th>
                    <th>Grade</th>
                    <th>Guardian</th>
                    <th>Status</th>
                    <th>Enrollment Date</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let student of students">
                    <td>
                      <strong>{{ student.student_id }}</strong>
                    </td>
                    <td>
                      <div class="d-flex align-items-center">
                        <div class="avatar me-3">
                          <i class="fas fa-user-circle fa-2x text-muted"></i>
                        </div>
                        <div>
                          <div class="fw-bold">{{ student.first_name }} {{ student.last_name }}</div>
                          <small class="text-muted">{{ student.email || 'No email' }}</small>
                        </div>
                      </div>
                    </td>
                    <td>
                      {{ student.current_enrollment?.school_class?.grade_level || 'Not enrolled' }}
                    </td>
                    <td>
                      <div *ngIf="student.primary_guardian">
                        {{ student.primary_guardian.first_name }} {{ student.primary_guardian.last_name }}
                        <br>
                        <small class="text-muted">{{ student.primary_guardian.phone }}</small>
                      </div>
                      <span *ngIf="!student.primary_guardian" class="text-muted">No guardian</span>
                    </td>
                    <td>
                      <span class="badge" [ngClass]="{
                        'bg-success': student.status === 'active',
                        'bg-secondary': student.status === 'inactive',
                        'bg-primary': student.status === 'graduated',
                        'bg-warning': student.status === 'transferred'
                      }">
                        {{ student.status | titlecase }}
                      </span>
                    </td>
                    <td>
                      {{ student.enrollment_date | date:'MMM dd, yyyy' }}
                    </td>
                    <td>
                      <div class="btn-group" role="group">
                        <button 
                          class="btn btn-sm btn-outline-primary" 
                          [routerLink]="['/students', student.id]"
                          title="View Details">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button 
                          class="btn btn-sm btn-outline-secondary" 
                          [routerLink]="['/students', student.id, 'edit']"
                          title="Edit">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button 
                          class="btn btn-sm btn-outline-danger" 
                          (click)="deleteStudent(student)"
                          title="Delete">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Pagination -->
            <nav *ngIf="!isLoading && students.length > 0" class="mt-4">
              <ul class="pagination justify-content-center">
                <li class="page-item" [class.disabled]="currentPage === 1">
                  <button class="page-link" (click)="goToPage(currentPage - 1)">Previous</button>
                </li>
                <li class="page-item" 
                    *ngFor="let page of getPageNumbers()" 
                    [class.active]="page === currentPage">
                  <button class="page-link" (click)="goToPage(page)">{{ page }}</button>
                </li>
                <li class="page-item" [class.disabled]="currentPage === totalPages">
                  <button class="page-link" (click)="goToPage(currentPage + 1)">Next</button>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </app-layout>
  `,
  styles: [`
    .student-list-container {
      max-width: 1200px;
    }

    .avatar {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .table th {
      border-top: none;
      font-weight: 600;
      color: #374151;
    }

    .btn-group .btn {
      border-radius: 6px;
      margin-right: 2px;
    }

    .card {
      border: none;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .card-header {
      background: white;
      border-bottom: 1px solid #e5e7eb;
      border-radius: 12px 12px 0 0 !important;
    }

    .pagination .page-link {
      border-radius: 6px;
      margin: 0 2px;
      border: 1px solid #e5e7eb;
    }

    .pagination .page-item.active .page-link {
      background: #4f46e5;
      border-color: #4f46e5;
    }
  `]
})
export class StudentListComponent implements OnInit {
  private studentService = inject(StudentService);

  students: Student[] = [];
  isLoading = true;
  searchTerm = '';
  selectedStatus = '';
  selectedGrade = '';
  currentPage = 1;
  totalPages = 1;
  totalStudents = 0;
  perPage = 15;

  ngOnInit(): void {
    this.loadStudents();
  }

  loadStudents(): void {
    this.isLoading = true;
    
    const params: QueryParams = {
      page: this.currentPage,
      per_page: this.perPage
    };

    if (this.searchTerm) {
      params.search = this.searchTerm;
    }

    if (this.selectedStatus) {
      params.status = this.selectedStatus;
    }

    if (this.selectedGrade) {
      params.grade_level = this.selectedGrade;
    }

    this.studentService.getStudents(params).subscribe({
      next: (response: PaginatedResponse<Student>) => {
        this.students = response.data;
        this.currentPage = response.current_page;
        this.totalPages = response.last_page;
        this.totalStudents = response.total;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading students:', error);
        this.isLoading = false;
      }
    });
  }

  search(): void {
    this.currentPage = 1;
    this.loadStudents();
  }

  filterByStatus(): void {
    this.currentPage = 1;
    this.loadStudents();
  }

  filterByGrade(): void {
    this.currentPage = 1;
    this.loadStudents();
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedStatus = '';
    this.selectedGrade = '';
    this.currentPage = 1;
    this.loadStudents();
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.loadStudents();
    }
  }

  getPageNumbers(): number[] {
    const pages: number[] = [];
    const start = Math.max(1, this.currentPage - 2);
    const end = Math.min(this.totalPages, this.currentPage + 2);

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    return pages;
  }

  deleteStudent(student: Student): void {
    if (confirm(`Are you sure you want to delete ${student.first_name} ${student.last_name}?`)) {
      this.studentService.deleteStudent(student.id).subscribe({
        next: () => {
          this.loadStudents();
        },
        error: (error) => {
          console.error('Error deleting student:', error);
          alert('Error deleting student. Please try again.');
        }
      });
    }
  }
}
